{
    "name": "Deno",
    "build": {
        "dockerfile": "Dockerfile"
    },

    // Features to add to the dev container. More info: https://containers.dev/features.
    // "features": {},

    // Use 'forwardPorts' to make a list of ports inside the container available locally.
    // "forwardPorts": [],

    // Use 'postCreateCommand' to run commands after the container is created.
    // "postCreateCommand": "yarn install",

    // Configure tool-specific properties.
    "customizations": {
        "vscode": {
            "extensions": [
                "denoland.vscode-deno"
            ]
        }
    }
    // Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
    // "remoteUser": "root"
}
